import React, { useState } from 'react';
import {
  <PERSON>I<PERSON>,
  ListItemText,
  ListItemAvatar,
  Typography,
  IconButton,
  Box,
  Chip,
  Tooltip,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  Stack,
  Menu,
  MenuItem,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Star,
  StarBorder,
  Delete,
  ContentCopy,
  Image,
  InsertDriveFile,
  ExpandMore,
  ExpandLess,
  Edit,
  LocalOffer,
  MoreVert,
  TextSnippet,
} from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '@/store';
import { toggleFavorite, deleteEntry, updateEntryName, updateEntryTags } from '@/store/slices/clipboardSlice';
import type { ClipboardEntry as ClipboardEntryType } from '@/types/clipboard';

interface ClipboardEntryProps {
  entry: ClipboardEntryType;
  isSelected: boolean;
  onClick: () => void;
  onCopy: () => void;
}

const ClipboardEntry: React.FC<ClipboardEntryProps> = ({
  entry,
  isSelected,
  onClick,
  onCopy,
}) => {
  const dispatch = useAppDispatch();
  const { history, favorites } = useAppSelector((state) => state.clipboard);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editName, setEditName] = useState(entry.name || '');
  const [editTags, setEditTags] = useState<string[]>(entry.tags || []);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Get all existing tags for autocomplete
  const allExistingTags = React.useMemo(() => {
    const tagSet = new Set<string>();
    [...history, ...favorites].forEach(e => {
      e.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [history, favorites]);

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(toggleFavorite(entry.id));
  };

  const handleDelete = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setAnchorEl(null);
    dispatch(deleteEntry(entry.id));
  };

  const handleCopy = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    onCopy();
  };

  const handleMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setAnchorEl(e.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const minutes = Math.floor(diffInHours * 60);
      return `${minutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getTypeIcon = () => {
    switch (entry.type) {
      case 'image':
        return <Image />;
      case 'file':
        return <InsertDriveFile />;
      default:
        return <TextSnippet />;
    }
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleEdit = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setAnchorEl(null);
    setEditName(entry.name || '');
    setEditTags(entry.tags || []);
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    try {
      if (editName !== (entry.name || '')) {
        await dispatch(updateEntryName({ entryId: entry.id, name: editName })).unwrap();
      }
      if (JSON.stringify(editTags.sort()) !== JSON.stringify((entry.tags || []).sort())) {
        await dispatch(updateEntryTags({ entryId: entry.id, tags: editTags })).unwrap();
      }
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Failed to update entry:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditName(entry.name || '');
    setEditTags(entry.tags || []);
    setIsEditDialogOpen(false);
  };

  const shouldShowExpandButton = entry.content.length > 200;
  const displayContent = isExpanded ? entry.content : entry.content.substring(0, 200);

  return (
    <>
      <ListItem
        component="div"
        sx={{
          cursor: 'pointer',
          border: isSelected ? 2 : 1,
          borderColor: isSelected ? 'primary.main' : 'divider',
          backgroundColor: isSelected ? 'action.selected' : 'background.paper',
          borderRadius: 1,
          mb: 1,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            backgroundColor: 'action.hover',
            borderColor: 'primary.light',
          },
        }}
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Type Icon Avatar */}
        <ListItemAvatar>
          <Avatar
            sx={{
              bgcolor: 'primary.main',
              width: 32,
              height: 32,
            }}
          >
            {getTypeIcon()}
          </Avatar>
        </ListItemAvatar>

        {/* Main Content */}
        <ListItemText
          primary={
            <Box>
              {/* Header with metadata */}
              <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ fontSize: '0.75rem' }}
                >
                  {formatTimestamp(entry.timestamp)}
                </Typography>
                {entry.metadata?.source && (
                  <Chip
                    label={entry.metadata.source}
                    size="small"
                    variant="outlined"
                    sx={{ height: 18, fontSize: '0.65rem' }}
                  />
                )}
                {/* Favorite star - always visible */}
                <Tooltip title={entry.isFavorite ? 'Remove from favorites' : 'Add to favorites'}>
                  <IconButton
                    size="small"
                    onClick={handleToggleFavorite}
                    color={entry.isFavorite ? 'warning' : 'default'}
                    sx={{ ml: 'auto', p: 0.5 }}
                  >
                    {entry.isFavorite ? <Star fontSize="small" /> : <StarBorder fontSize="small" />}
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Name and Tags */}
              {(entry.name || (entry.tags && entry.tags.length > 0)) && (
                <Box mb={1}>
                  {entry.name && (
                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: 'text.primary',
                      }}
                    >
                      {entry.name}
                    </Typography>
                  )}
                  {entry.tags && entry.tags.length > 0 && (
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {entry.tags.map((tag) => (
                        <Chip
                          key={tag}
                          label={tag}
                          size="small"
                          icon={<LocalOffer fontSize="small" />}
                          variant="outlined"
                          sx={{ height: 18, fontSize: '0.65rem' }}
                        />
                      ))}
                    </Box>
                  )}
                </Box>
              )}

              {/* Content */}
              <Box>
                <Typography
                  variant="body2"
                  sx={{
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    fontFamily: entry.type === 'text' ? 'inherit' : 'monospace',
                    fontSize: entry.type === 'text' ? '0.875rem' : '0.75rem',
                    lineHeight: 1.4,
                  }}
                >
                  {displayContent}
                  {shouldShowExpandButton && !isExpanded && '...'}
                </Typography>

                {shouldShowExpandButton && (
                  <Button
                    size="small"
                    onClick={handleToggleExpand}
                    startIcon={isExpanded ? <ExpandLess /> : <ExpandMore />}
                    sx={{ mt: 1, minHeight: 'auto', p: 0.5 }}
                  >
                    {isExpanded ? 'Show Less' : 'Show More'}
                  </Button>
                )}
              </Box>

              {/* Image Preview */}
              {entry.preview && entry.type === 'image' && (
                <Box mt={1}>
                  <img
                    src={entry.preview}
                    alt="Clipboard preview"
                    style={{
                      maxWidth: '100%',
                      maxHeight: 80,
                      borderRadius: 4,
                      objectFit: 'contain',
                    }}
                  />
                </Box>
              )}

              {/* Size Info */}
              {entry.metadata?.size && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: 'block' }}
                >
                  Size: {(entry.metadata.size / 1024).toFixed(1)} KB
                </Typography>
              )}
            </Box>
          }
        />

        {/* Action Buttons - Show on Hover */}
        {isHovered && (
          <Box display="flex" gap={0.5} ml={1}>
            <Tooltip title="Copy to clipboard">
              <IconButton size="small" onClick={handleCopy} color="primary">
                <ContentCopy fontSize="small" />
              </IconButton>
            </Tooltip>

            <Tooltip title="More options">
              <IconButton size="small" onClick={handleMoreClick}>
                <MoreVert fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </ListItem>

      {/* More Options Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleEdit}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Edit Dialog */}
      <Dialog
        open={isEditDialogOpen}
        onClose={handleCancelEdit}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Edit Entry</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Name"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              fullWidth
              placeholder="Enter a name for this entry (optional)"
              data-testid="edit-name-input"
            />

            <Autocomplete
              multiple
              freeSolo
              options={allExistingTags}
              value={editTags}
              onChange={(_, newValue) => setEditTags(newValue)}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    {...getTagProps({ index })}
                    key={option}
                    label={option}
                    size="small"
                    icon={<LocalOffer fontSize="small" />}
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Add tags..."
                  data-testid="edit-tags-input"
                />
              )}
              data-testid="edit-tags-autocomplete"
            />

            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Content Preview:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  p: 1,
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 1,
                  backgroundColor: 'grey.50',
                  maxHeight: 100,
                  overflow: 'auto',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  fontSize: '0.75rem',
                }}
              >
                {entry.content.substring(0, 200)}
                {entry.content.length > 200 && '...'}
              </Typography>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelEdit}>Cancel</Button>
          <Button onClick={handleSaveEdit} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ClipboardEntry;
