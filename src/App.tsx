import React, { useEffect } from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { Provider } from 'react-redux';
import { store, useAppDispatch, useAppSelector } from '@/store';
import { loadPreferences } from '@/store/slices/preferencesSlice';
import { useClipboardMonitoring } from '@/hooks/useClipboardMonitoring';
import { getTheme } from '@/theme';
import AppLayout from '@/components/AppLayout';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const theme = useAppSelector((state) => state.preferences.theme);

  // Initialize clipboard monitoring
  useClipboardMonitoring();

  useEffect(() => {
    // Initialize the app
    const initializeApp = async () => {
      try {
        // Load user preferences
        await dispatch(loadPreferences()).unwrap();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [dispatch]);

  return (
    <ThemeProvider theme={getTheme(theme)}>
      <CssBaseline />
      <AppLayout />
    </ThemeProvider>
  );
};

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
